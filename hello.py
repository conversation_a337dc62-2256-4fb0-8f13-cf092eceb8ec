import equinox as eqx
import jax.random as jrandom
from jax import Array
from jaxtyping import Array, Float  # 使用 jaxtyping 库可以提供更丰富的数组类型提示

# 定义一个类型别名来表示 JAX 的伪随机数生成器密钥
PRNGKey = Array


class Linear(eqx.Module):
    """一个简单的线性层，执行 y = Wx + b。"""

    # --- 属性注解 ---
    # weight 是一个二维浮点数数组，维度为 (输出维度, 输入维度)
    weight: Float[Array, "out_size in_size"]
    # bias 是一个一维浮点数数组，维度为 (输出维度)
    bias: Float[Array, "out_size"]

    def __init__(self, in_size: int, out_size: int, *, key: PRNGKey):
        """
        构造函数。

        Args:
            in_size (int): 输入特征的数量。
            out_size (int): 输出特征的数量。
            key (PRNGKey): 用于参数初始化的 JAX 随机密钥。
        """
        # 从主密钥中分裂出两个子密钥，分别用于权重和偏置
        wkey, bkey = jrandom.split(key)

        # 使用正态分布初始化权重和偏置
        self.weight = jrandom.normal(wkey, (out_size, in_size))
        self.bias = jrandom.normal(bkey, (out_size,))

    def __call__(self, x: Float[Array, "in_size"]) -> Float[Array, "out_size"]:
        """
        前向传播方法。

        Args:
            x (Float[Array, "in_size"]): 输入向量。

        Returns:
            Float[Array, "out_size"]: 输出向量。
        """
        # 执行矩阵乘法并加上偏置
        return self.weight @ x + self.bias


# --- 使用示例 ---

# 创建一个 JAX 随机密钥
key = jrandom.PRNGKey(0)

# 分裂密钥以用于模型实例化和数据创建
model_key, data_key = jrandom.split(key)

# 定义模型维度
in_features = 2
out_features = 4

# 实例化线性层
model = Linear(in_size=in_features, out_size=out_features, key=model_key)

# 创建一个随机的输入向量
# 形状为 (in_features,) = (2,)
input_data = jrandom.normal(data_key, (in_features,))

# 调用模型进行前向传播
output_data = model(input_data)

print("--- 模型结构 ---")
print(model)
print("\n--- 输入数据 ---")
print(input_data)
print(f"输入形状: {input_data.shape}")
print("\n--- 输出数据 ---")
print(output_data)
print(f"输出形状: {output_data.shape}")
